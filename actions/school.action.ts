'use server';

import {
  createSchool as apiCreateSchool,
  assignSchoolManager,
  getAllSchools,
  getSchoolById,
  updateSchool,
  deleteSchool,
  createSchoolIndependent,
  getMySchool as apiGetMySchool,
  updateMySchool as apiUpdateMySchool,
  ICreateSchoolPayload,
  IAssignSchoolManagerPayload,
  IUpdateSchoolPayload,
  ISchoolResponse,
} from '@/apis/schoolApi';
import { TTransformResponse } from '@/apis/transformResponse';
import { revalidatePath } from 'next/cache';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/config/auth';
import { EUserRole } from '@/config/enums/user';
import { z } from 'zod';

// Define specific paths for revalidation
const USERS_MANAGEMENT_PATH = '/users-management';
const USER_CREATE_PATH = '/users-management/create';
const SCHOOL_MANAGEMENT_PATH = '/school-management';

// Validation schemas for FormData
const createSchoolSchema = z.object({
  name: z.string().min(1, "School name is required").max(255, "School name is too long"),
  address: z.string().optional(),
  phoneNumber: z.string().optional(),
  registeredNumber: z.string().optional(),
  email: z.string().email("Invalid email format").optional().or(z.literal("")),
  brandId: z.string().uuid("Invalid brand ID format").optional().or(z.literal(""))
});

const updateSchoolSchema = z.object({
  name: z.string().min(1, "School name is required").max(255, "School name is too long").optional(),
  address: z.string().optional(),
  phoneNumber: z.string().optional(),
  registeredNumber: z.string().optional(),
  email: z.string().email("Invalid email format").optional().or(z.literal("")),
  brandId: z.string().uuid("Invalid brand ID format").optional().or(z.literal(""))
});

// Utility function to parse FormData to object
function parseFormDataToObject(formData: FormData): Record<string, string> {
  const data: Record<string, string> = {};
  for (const [key, value] of formData.entries()) {
    if (typeof value === 'string') {
      data[key] = value.trim();
    }
  }
  return data;
}

/**
 * Fetches all schools.
 * @returns A list of all schools.
 */
export async function handleGetAllSchoolsAction(): Promise<TTransformResponse<ISchoolResponse[]>> {
  try {
    const response = await getAllSchools();
    return response;
  } catch (error: any) {
    console.error('Error in handleGetAllSchoolsAction:', error);
    return { status: 'error', message: error.message || 'An unexpected server error occurred.' };
  }
}

/**
 * Updates a school's information.
 * @param schoolId - The ID of the school to update.
 * @param payload - The school data to update.
 * @returns The updated school.
 */
export async function handleUpdateSchoolAction(
  schoolId: string,
  payload: IUpdateSchoolPayload
): Promise<TTransformResponse<ISchoolResponse>> {
  try {
    // Get the current user's session
    const session = await getServerSession(authOptions);
    const currentUserRole = session?.user?.role;

    // Authorization check: Only SUPER_ADMIN or the assigned SCHOOL_MANAGER can update
    if (currentUserRole !== EUserRole.SUPER_ADMIN) {
      const schoolResponse = await getSchoolById(schoolId);
      // if (schoolResponse.status === 'success' && schoolResponse.data?.admin?.id !== currentUserId) {
      //   return { status: 'error', message: 'Unauthorized: You are not allowed to update this school.' };
      // }
      if (schoolResponse.status === 'error') {
        return { status: 'error', message: schoolResponse.message || 'Failed to fetch school for authorization.' };
      }
    }

    const response = await updateSchool(schoolId, payload);

    if (response.status === 'success') {
      // Revalidate relevant paths
      revalidatePath(USERS_MANAGEMENT_PATH); // General user list
      revalidatePath(`${USERS_MANAGEMENT_PATH}/edit/${response.data?.admin?.id}`); // Admin's edit page if applicable
      revalidatePath(`/school-management/${schoolId}`); // School detail page
      // Add any other paths that display school information
    }
    return response;
  } catch (error: any) {
    console.error('Error in handleUpdateSchoolAction:', error);
    return { status: 'error', message: error.message || 'An unexpected server error occurred while updating the school.' };
  }
}

/**
 * Creates a new school.
 * @param payload - The school data.
 * @returns The created school.
 */
export async function handleCreateSchoolAction(
  payload: ICreateSchoolPayload
): Promise<TTransformResponse<ISchoolResponse>> {
  try {
    // Get the current user's session
    const session = await getServerSession(authOptions);
    const userId = session?.user?.id;
    const userRole = session?.user?.role;

    // If the user is logged in, set the adminId to the current user's ID
    // This ensures the school is assigned to the user who created it
    if (userId) {
      // If the user has the SCHOOL_MANAGER role, set the adminId to the current user's ID
      if (userRole === EUserRole.SCHOOL_MANAGER) {
        payload.adminId = userId;
      }
    }

    const response = await apiCreateSchool(payload);

    if (response.status === 'success') {
      // Revalidate the users management and user creation pages
      revalidatePath(USERS_MANAGEMENT_PATH);
      revalidatePath(USER_CREATE_PATH);
    }
    return response;
  } catch (error: any) {
    console.error('Error in handleCreateSchoolAction:', error);
    return { status: 'error', message: error.message || 'An unexpected server error occurred.' };
  }
}

/**
 * Assigns a school manager to a school.
 * @param schoolId - The ID of the school to update.
 * @param payload - The data containing the adminId to assign.
 * @returns The updated school.
 */
export async function handleAssignSchoolManagerAction(
  schoolId: string,
  payload: IAssignSchoolManagerPayload
): Promise<TTransformResponse<ISchoolResponse>> {
  try {
    const response = await assignSchoolManager(schoolId, payload);

    if (response.status === 'success') {
      // Revalidate the users management and user creation pages
      revalidatePath(USERS_MANAGEMENT_PATH);
      revalidatePath(USER_CREATE_PATH);
    }
    return response;
  } catch (error: any) {
    console.error('Error in handleAssignSchoolManagerAction:', error);
    return { status: 'error', message: error.message || 'An unexpected server error occurred.' };
  }
}

/**
 * Fetches a school by ID.
 * @param schoolId - The ID of the school to fetch.
 * @returns The school details.
 */
export async function handleGetSchoolByIdAction(
  schoolId: string
): Promise<TTransformResponse<ISchoolResponse>> {
  try {
    if (!schoolId) {
      return { status: 'error', message: 'School ID is required.' };
    }

    const response = await getSchoolById(schoolId);
    return response;
  } catch (error: any) {
    console.error('Error in handleGetSchoolByIdAction:', error);
    return { status: 'error', message: error.message || 'An unexpected server error occurred.' };
  }
}

/**
 * Deletes a school by ID.
 * @param schoolId - The ID of the school to delete.
 * @returns A success or error message.
 */
export async function handleDeleteSchoolAction(
  schoolId: string
): Promise<TTransformResponse<null>> { // Assuming delete returns no specific data on success
  try {
    // Get the current user's session
    const session = await getServerSession(authOptions);
    const currentUserRole = session?.user?.role;

    // Authorization check: Only SUPER_ADMIN can delete schools (adjust as needed)
    if (currentUserRole !== EUserRole.SUPER_ADMIN) {
      return { status: 'error', message: 'Unauthorized: You are not allowed to delete this school.' };
    }

    // Call the deleteSchool API function
    const response = await deleteSchool(schoolId);

    if (response.status === 'success') {
      // Revalidate paths that list or display schools
      revalidatePath(USERS_MANAGEMENT_PATH); // If school deletion affects user assignments
      revalidatePath('/school-management'); // The main schools listing page
      // Add any other relevant paths, e.g., if schools are listed elsewhere
    }
    return response;
  } catch (error: any) {
    console.error('Error in handleDeleteSchoolAction:', error);
    return { status: 'error', message: error.message || 'An unexpected server error occurred while deleting the school.' };
  }
}

// ============================================================================
// TASK 2: New Server Actions for INDEPENDENT_TEACHER School Management
// ============================================================================

/**
 * Creates a new school using FormData (Task 2 requirement).
 * Handles POST requests for school creation with INDEPENDENT_TEACHER authorization.
 * @param formData - FormData containing school information
 * @returns The created school or error response
 */
export async function createSchool(formData: FormData): Promise<TTransformResponse<ISchoolResponse>> {
  try {
    // Get the current user's session
    const session = await getServerSession(authOptions);
    const userId = session?.user?.id;
    const userRole = session?.user?.role;
    const userSchoolId = session?.user?.schoolId;

    // Authentication check
    if (!session || !userId) {
      return { status: 'error', message: 'Authentication required. Please sign in to create a school.' };
    }

    // Authorization check: Only INDEPENDENT_TEACHER can create schools via this action
    if (userRole !== EUserRole.INDEPENDENT_TEACHER) {
      return { status: 'error', message: 'Unauthorized: Only independent teachers can create schools.' };
    }

    // Check if user already has a school (one-school limit for INDEPENDENT_TEACHER)
    if (userSchoolId) {
      return { status: 'error', message: 'You already have a school. Independent teachers can only create one school.' };
    }

    // Parse and validate FormData
    const rawData = parseFormDataToObject(formData);
    const validationResult = createSchoolSchema.safeParse(rawData);

    if (!validationResult.success) {
      const errorMessages = validationResult.error.errors.map(err => err.message).join(', ');
      return { status: 'error', message: errorMessages };
    }

    const validatedData = validationResult.data;

    // Prepare payload for API call
    const payload: ICreateSchoolPayload = {
      name: validatedData.name,
      address: validatedData.address || '',
      phoneNumber: validatedData.phoneNumber || '',
      registeredNumber: validatedData.registeredNumber || '',
      email: validatedData.email || '',
      // adminId will be set automatically by the backend for INDEPENDENT_TEACHER
      ...(validatedData.brandId && validatedData.brandId !== '' && { brandId: validatedData.brandId })
    };

    // Call the API to create school
    const response = await createSchoolIndependent(payload);

    if (response.status === 'success') {
      // Revalidate relevant paths
      revalidatePath(SCHOOL_MANAGEMENT_PATH);
      revalidatePath('/');
    }

    return response;
  } catch (error: any) {
    console.error('Error in createSchool server action:', error);
    return { status: 'error', message: error.message || 'An unexpected server error occurred while creating the school.' };
  }
}

/**
 * Gets the current user's school (Task 2 requirement).
 * Handles GET requests to retrieve the authenticated user's school.
 * @returns The user's school data or null if no school exists
 */
export async function getMySchool(): Promise<TTransformResponse<ISchoolResponse | null>> {
  try {
    // Get the current user's session
    const session = await getServerSession(authOptions);
    const userId = session?.user?.id;

    // Authentication check
    if (!session || !userId) {
      return { status: 'error', message: 'Authentication required. Please sign in to access your school information.' };
    }

    // Call the API to get user's school
    const response = await apiGetMySchool();
    return response;
  } catch (error: any) {
    console.error('Error in getMySchool server action:', error);
    return { status: 'error', message: error.message || 'An unexpected server error occurred while fetching your school.' };
  }
}

/**
 * Updates the current user's school using FormData (Task 2 requirement).
 * Handles PATCH requests for school updates with INDEPENDENT_TEACHER authorization.
 * @param formData - FormData containing school update information
 * @returns The updated school or error response
 */
export async function updateMySchool(formData: FormData): Promise<TTransformResponse<ISchoolResponse>> {
  try {
    // Get the current user's session
    const session = await getServerSession(authOptions);
    const userId = session?.user?.id;
    const userRole = session?.user?.role;
    const userSchoolId = session?.user?.schoolId;

    // Authentication check
    if (!session || !userId) {
      return { status: 'error', message: 'Authentication required. Please sign in to update your school.' };
    }

    // Authorization check: Only INDEPENDENT_TEACHER can update their school via this action
    if (userRole !== EUserRole.INDEPENDENT_TEACHER) {
      return { status: 'error', message: 'Unauthorized: Only independent teachers can update their school.' };
    }

    // Check if user has a school to update
    if (!userSchoolId) {
      return { status: 'error', message: 'School not found. You must create a school before you can update it.' };
    }

    // Parse and validate FormData
    const rawData = parseFormDataToObject(formData);
    const validationResult = updateSchoolSchema.safeParse(rawData);

    if (!validationResult.success) {
      const errorMessages = validationResult.error.errors.map(err => err.message);
      return { status: 'error', message: errorMessages };
    }

    const validatedData = validationResult.data;

    // Prepare payload for API call (only include fields that are provided)
    const payload: IUpdateSchoolPayload = {};
    if (validatedData.name !== undefined) payload.name = validatedData.name;
    if (validatedData.address !== undefined) payload.address = validatedData.address;
    if (validatedData.phoneNumber !== undefined) payload.phoneNumber = validatedData.phoneNumber;
    if (validatedData.registeredNumber !== undefined) payload.registeredNumber = validatedData.registeredNumber;
    if (validatedData.email !== undefined) payload.email = validatedData.email;

    // Call the API to update school
    const response = await apiUpdateMySchool(payload);

    if (response.status === 'success') {
      // Revalidate relevant paths
      revalidatePath(SCHOOL_MANAGEMENT_PATH);
      revalidatePath(`${SCHOOL_MANAGEMENT_PATH}/${userSchoolId}`);
      revalidatePath('/');
    }

    return response;
  } catch (error: any) {
    console.error('Error in updateMySchool server action:', error);
    return { status: 'error', message: error.message || 'An unexpected server error occurred while updating your school.' };
  }
}

/**
 * Simple utility function to fetch all schools for client components.
 * @returns A list of all schools with basic information.
 */
export async function fetchSchools() {
  try {
    const response = await getAllSchools();
    if (response.status === 'success' && response.data) {
      return response.data;
    }
    return [];
  } catch (error) {
    console.error('Error in fetchSchools:', error);
    return [];
  }
}
