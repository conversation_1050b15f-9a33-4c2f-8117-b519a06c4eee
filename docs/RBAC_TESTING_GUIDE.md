# RBAC Testing Guide for INDEPENDENT_TEACHER Role

This document provides a comprehensive guide for testing the RBAC (Role-Based Access Control) implementation for the `INDEPENDENT_TEACHER` role.

## Overview

The RBAC implementation restricts `INDEPENDENT_TEACHER` users from accessing user management functionalities while allowing them to manage their own school and access worksheet management features.

## What Was Implemented

### 1. Middleware Updates (`middleware.ts`)
- Added route protection for `INDEPENDENT_TEACHER` role
- Blocked access to `/users-management` routes (ADMIN only)
- Blocked access to `/teacher-management` routes (SCHOOL_MANAGER only)
- Added security logging for unauthorized access attempts
- Allowed access to `/manage-worksheet` routes for `INDEPENDENT_TEACHER`

### 2. Enhanced Error Handling (`apis/transformResponse.ts`)
- Added user-friendly error messages for 403 Forbidden responses
- Enhanced error response structure with status codes and error codes
- Improved client-side error handling

### 3. RBAC Utility (`utils/rbacErrorHandler.ts`)
- Created comprehensive RBAC error handling utility
- Added specific error messages for `INDEPENDENT_TEACHER` role
- Implemented security logging for RBAC violations
- Added permission checking functions

### 4. API Request Enhancements
- Updated both client-side (`apis/clientRequest.ts`) and server-side (`apis/request.ts`) request handlers
- Added 403 error logging and enhanced error handling
- Integrated with RBAC utility for consistent error messages

## Manual Testing Scenarios

### Scenario 1: INDEPENDENT_TEACHER Route Access

#### Test Case 1.1: Blocked Routes
**Objective**: Verify that `INDEPENDENT_TEACHER` cannot access user management routes

**Steps**:
1. Sign in as a user with `INDEPENDENT_TEACHER` role
2. Attempt to navigate to the following URLs directly in the browser:
   - `/users-management`
   - `/users-management/create`
   - `/users-management/edit/[any-id]`
   - `/teacher-management`
   - `/teacher-management/create`
   - `/teacher-management/edit/[any-id]`

**Expected Results**:
- User should be redirected to the home page (`/`)
- Security logs should appear in the browser console with unauthorized access attempts
- No error pages should be displayed to the user

#### Test Case 1.2: Allowed Routes
**Objective**: Verify that `INDEPENDENT_TEACHER` can access permitted routes

**Steps**:
1. Sign in as a user with `INDEPENDENT_TEACHER` role
2. Navigate to the following URLs:
   - `/` (home page)
   - `/manage-worksheet`
   - `/profile`
   - `/school-management` (if they have a school)

**Expected Results**:
- All routes should be accessible
- No redirects or errors should occur
- User should see appropriate content for their role

### Scenario 2: API Error Handling

#### Test Case 2.1: 403 API Responses
**Objective**: Verify that 403 API responses are handled gracefully

**Steps**:
1. Sign in as a user with `INDEPENDENT_TEACHER` role
2. Use browser developer tools to monitor network requests
3. Attempt to trigger API calls to user management endpoints (if any UI elements are mistakenly visible)

**Expected Results**:
- 403 responses should be logged in the console
- User-friendly error messages should be displayed
- No technical error details should be exposed to the user

### Scenario 3: Role Comparison Testing

#### Test Case 3.1: ADMIN Access
**Objective**: Verify that ADMIN users can access all routes

**Steps**:
1. Sign in as a user with `ADMIN` role
2. Navigate to all user management routes
3. Verify full access to all functionalities

**Expected Results**:
- All routes should be accessible
- All user management features should work correctly

#### Test Case 3.2: SCHOOL_MANAGER Access
**Objective**: Verify that SCHOOL_MANAGER users can access teacher management

**Steps**:
1. Sign in as a user with `SCHOOL_MANAGER` role
2. Navigate to teacher management routes
3. Verify access to teacher management features

**Expected Results**:
- Teacher management routes should be accessible
- User management routes should be blocked (ADMIN only)

## Security Logging Verification

### Console Logs to Monitor

When testing unauthorized access attempts, look for these log entries in the browser console:

```javascript
// Middleware logging
🚨 Unauthorized Access Attempt: {
  "timestamp": "2024-01-XX...",
  "event": "UNAUTHORIZED_ACCESS_ATTEMPT",
  "userId": "user-id",
  "userRole": "independent_teacher",
  "attemptedPath": "/users-management",
  "userAgent": "...",
  "ip": "..."
}

// RBAC violation logging
🚨 RBAC Violation: {
  "timestamp": "2024-01-XX...",
  "event": "RBAC_VIOLATION",
  "severity": "WARNING",
  "attemptedAction": "GET /user",
  "resource": "/user"
}
```

## Error Messages to Verify

### User-Friendly Messages
- "You do not have permission to perform this action. Please contact your administrator if you believe this is an error."
- "As an Independent Teacher, you don't have permission to manage other users. You can only manage your own profile and school."

### Technical Logs (Console Only)
- "403 Forbidden Response: ..." with detailed error information
- RBAC violation logs with user and action details

## Testing Checklist

- [ ] INDEPENDENT_TEACHER blocked from `/users-management` routes
- [ ] INDEPENDENT_TEACHER blocked from `/teacher-management` routes  
- [ ] INDEPENDENT_TEACHER can access `/manage-worksheet` routes
- [ ] INDEPENDENT_TEACHER can access `/profile` route
- [ ] Security logging works for unauthorized attempts
- [ ] User-friendly error messages displayed for 403 responses
- [ ] ADMIN users can access all routes
- [ ] SCHOOL_MANAGER users can access teacher management
- [ ] No technical error details exposed to users
- [ ] Console logs contain proper security information

## Troubleshooting

### Common Issues

1. **Redirects not working**: Check that middleware is properly configured and Next.js is restarted
2. **Logging not appearing**: Verify console is open and check for JavaScript errors
3. **Error messages not user-friendly**: Check that transformResponse.ts changes are applied
4. **Routes still accessible**: Clear browser cache and verify user role in session

### Debug Commands

```javascript
// Check current user session in browser console
console.log(await fetch('/api/auth/session').then(r => r.json()));

// Check middleware configuration
console.log('Middleware config loaded');
```

## Production Considerations

1. **Logging Service**: Replace console logging with proper logging service (DataDog, CloudWatch, etc.)
2. **Rate Limiting**: Consider adding rate limiting for repeated unauthorized access attempts
3. **Monitoring**: Set up alerts for excessive RBAC violations
4. **Audit Trail**: Implement comprehensive audit logging for security compliance

## Next Steps

After successful testing:
1. Deploy to staging environment for integration testing
2. Conduct security review with team
3. Update user documentation with role-specific features
4. Plan user training for INDEPENDENT_TEACHER role limitations
