type TValidationError = {
  field: string;
  constraints: string;
};

export type TApiError = {
  status: 'error';
  message: string | TValidationError[];
  statusCode?: number;
  code?: string; // Add error code for programmatic handling
};

export type TSuccess<T> = {
  status: 'success';
  data: T;
};

export type TTransformResponse<T> = TSuccess<T> | TApiError;

export type TTransformPaginationResponse<T> = {
  data: T[];
  count: number;
  total: number;
  page: number;
  pageCount: number;
};

/**
 * Get user-friendly error message for specific HTTP status codes
 */
function getUserFriendlyErrorMessage(statusCode: number, originalMessage?: string): string {
  switch (statusCode) {
    case 403:
      return 'You do not have permission to perform this action. Please contact your administrator if you believe this is an error.';
    case 401:
      return 'Your session has expired. Please sign in again.';
    case 404:
      return 'The requested resource was not found.';
    case 500:
      return 'An internal server error occurred. Please try again later.';
    default:
      return originalMessage || 'An unexpected error occurred.';
  }
}

export const transformResponse = async <T>(response: Response): Promise<TTransformResponse<T>> => {
  try {
    // Handle 204 No Content responses (like successful DELETE operations)
    if (response.status === 204) {
      return { status: 'success', data: { message: 'Operation completed successfully' } as unknown as T };
    }

    const data: any = await response.json();
    if (response.ok) {
      // Log the raw data structure to help confirm where 'items' are.
      // console.log('Raw data for mapping:', JSON.stringify(data, null, 2));

      let mappedData: any;

      // Check if the response has the "meta" structure for pagination
      if (data.meta && data.items !== undefined) {
        // Assuming 'items' is a top-level array alongside 'meta'
        mappedData = {
          items: data.items,
          currentPage: data.meta.page,
          pageSize: data.meta.pageSize,
          totalItems: data.meta.total,
          totalPages: data.meta.totalPages,
        };
      } else if (data?.data !== undefined) {
        // Fallback for existing structure where data is nested under a 'data' property
        mappedData = data.data;
      } else {
        // Fallback for direct data structure (if it matches TPagination<T> directly)
        mappedData = data;
      }
      // Ensure the mappedData conforms to T, which for worksheets is TPagination<TWorksheet>
      return { status: 'success', data: mappedData as T };
    }

    // Handle error responses
    const error = data as unknown as TApiError;
    const statusCode = response.status;

    // For 403 Forbidden responses, provide user-friendly message while preserving original for debugging
    if (statusCode === 403) {
      const userFriendlyMessage = getUserFriendlyErrorMessage(statusCode, error?.message as string);
      console.warn('403 Forbidden Response:', {
        originalMessage: error?.message,
        userFriendlyMessage,
        statusCode,
        url: response.url
      });

      return {
        status: 'error',
        message: userFriendlyMessage,
        statusCode,
        code: error?.code || 'RBAC_INSUFFICIENT_ROLE'
      };
    }

    // For other error status codes, use appropriate user-friendly messages
    if (statusCode >= 400) {
      const userFriendlyMessage = getUserFriendlyErrorMessage(statusCode, error?.message as string);
      return {
        status: 'error',
        message: userFriendlyMessage,
        statusCode,
        code: error?.code
      };
    }

    // Fallback for unexpected error format
    if (typeof error?.message === 'string') return { status: 'error', message: error.message as any, statusCode };
    return { status: 'error', message: error.message as any, statusCode };
  } catch (e) {
    console.error('Error in transformResponse:', e);
    return { status: 'error', message: 'Network error occurred. Please check your connection and try again.' };
  }
};
